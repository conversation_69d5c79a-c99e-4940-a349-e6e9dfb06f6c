#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
版本3 - 三阶段刺激态对比分析 (test1 vs test2 vs test3) - 使用优化后Test3数据

本脚本分析：
1. 三个刺激阶段的HEP时间序列对比（test1、test2、test3）
2. 三条HEP波形在同一图表中的可视化对比
3. 统一基线矫正确保三个阶段的基线一致性
4. Test3使用优化后数据（消除双峰现象，改善信号质量）

配色方案：深橙色(test1)、深紫色(test2)、深棕色(test3)
数据来源：Test1/Test2原始数据，Test3优化后数据
"""

import os
import h5py
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import font_manager
from scipy.signal import butter, filtfilt
from scipy.ndimage import gaussian_filter1d
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
font_path = r'C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf'
if os.path.exists(font_path):
    font_manager.fontManager.addfont(font_path)
    plt.rcParams['font.family'] = 'LXGW Wenkai'
plt.rcParams['font.size'] = 10
plt.rcParams['axes.unicode_minus'] = False

# 定义路径
BASE_DIR = r'D:\ecgeeg\30-数据分析\5-HBA'
DATA_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '13_raw_epochs', 'tp9tp10')
OPTIMIZATION_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '14_rest_vs_test_analysis', 'tp9tp10', 'test3_optimization')
RESULT_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '14_rest_vs_test_analysis', 'tp9tp10')
os.makedirs(RESULT_DIR, exist_ok=True)

# 定义脑区电极分组
BRAIN_REGIONS = {
    '左前额叶区域': ['Fp1', 'AF3', 'AF7', 'F3', 'F5'],
    '右前额叶区域': ['Fp2', 'AF4', 'AF8', 'F4', 'F6'],
    '额中央区域': ['Fpz', 'AFz', 'Fz', 'FCz'],
    '中央区域': ['Cz', 'CPz', 'Pz']
}

# 时间窗口设置
BASELINE_WINDOW = (-0.2, 0.0)  # 基线矫正窗口 -200ms到0ms

# 高对比度刺激态配色方案
PHASE_COLORS = {
    'Test1': '#FF4500',   # 深橙色
    'Test2': '#4B0082',   # 深紫色
    'Test3': '#8B4513'    # 深棕色
}

def load_hep_data(h5_path, max_epochs=3000):
    """加载HEP数据，对大数据集进行智能采样"""
    print(f"正在加载数据: {os.path.basename(h5_path)}")

    with h5py.File(h5_path, 'r') as f:
        data = f['data'][:]
        ch_names = [ch.decode('utf-8').strip() if isinstance(ch, bytes) else str(ch).strip()
                   for ch in f['ch_names'][:]]
        times = f['times'][:]

        if 'subject_ids' in f:
            subject_ids = f['subject_ids'][:]
            subject_ids = [s.decode('utf-8') if isinstance(s, bytes) else str(s)
                          for s in subject_ids]
        else:
            subject_ids = ['unknown'] * data.shape[0]

    # 智能采样：如果数据量过大，进行随机采样
    n_epochs = data.shape[0]
    if n_epochs > max_epochs:
        print(f"  数据量较大 ({n_epochs} epochs)，进行随机采样到 {max_epochs} epochs")
        np.random.seed(42)  # 确保可重复性
        sample_indices = np.random.choice(n_epochs, max_epochs, replace=False)
        sample_indices = np.sort(sample_indices)  # 保持时间顺序

        data = data[sample_indices]
        subject_ids = [subject_ids[i] for i in sample_indices]
        print(f"  ✅ 采样完成，保留 {len(sample_indices)} 个高质量epochs")

    sampling_freq = 1 / (times[1] - times[0])
    print(f"  最终数据形状: {data.shape}")
    print(f"  采样频率: {sampling_freq:.1f} Hz")
    print(f"  时间范围: {times[0]*1000:.1f} 到 {times[-1]*1000:.1f} ms")

    return data, ch_names, times, subject_ids, sampling_freq

def apply_bandpass_filter(data, sampling_freq, low_freq=0.5, high_freq=45):
    """应用带通滤波器"""
    print(f"  应用带通滤波: {low_freq}-{high_freq} Hz")

    nyquist = sampling_freq / 2
    low_norm = max(low_freq / nyquist, 0.001)
    high_norm = min(high_freq / nyquist, 0.999)

    b, a = butter(4, [low_norm, high_norm], btype='band')

    filtered_data = np.zeros_like(data)
    for i in range(data.shape[0]):
        for j in range(data.shape[1]):
            filtered_data[i, j, :] = filtfilt(b, a, data[i, j, :])

    return filtered_data

def apply_gaussian_smoothing(data, sigma=1.5):
    """应用高斯平滑"""
    print(f"  应用高斯平滑，标准差: {sigma}")

    smoothed_data = np.zeros_like(data)
    for i in range(data.shape[0]):
        for j in range(data.shape[1]):
            smoothed_data[i, j, :] = gaussian_filter1d(data[i, j, :], sigma=sigma)

    return smoothed_data

def apply_unified_baseline_correction_three_phases(test1_data, test2_data, test3_data, times):
    """三阶段刺激态统一基线矫正"""
    print("执行三阶段刺激态统一基线矫正...")

    baseline_mask = (times >= BASELINE_WINDOW[0]) & (times <= BASELINE_WINDOW[1])
    print(f"  基线窗口: {BASELINE_WINDOW[0]*1000:.0f} 到 {BASELINE_WINDOW[1]*1000:.0f} ms")

    test1_corrected = test1_data.copy()
    test2_corrected = test2_data.copy()
    test3_corrected = test3_data.copy()

    n_epochs_test1, n_channels, _ = test1_data.shape
    n_epochs_test2 = test2_data.shape[0]
    n_epochs_test3 = test3_data.shape[0]

    print(f"  处理 Test1: {n_epochs_test1} epochs, Test2: {n_epochs_test2} epochs, Test3: {n_epochs_test3} epochs")

    # 为每个电极计算全局基线参考
    for ch in range(n_channels):
        test1_baseline_all = test1_data[:, ch, baseline_mask]
        test2_baseline_all = test2_data[:, ch, baseline_mask]
        test3_baseline_all = test3_data[:, ch, baseline_mask]

        all_baseline_data = np.concatenate([test1_baseline_all.flatten(),
                                          test2_baseline_all.flatten(),
                                          test3_baseline_all.flatten()])
        global_baseline_mean = np.mean(all_baseline_data)

        # 对每个epoch进行基线矫正
        for epoch in range(n_epochs_test1):
            epoch_baseline_mean = np.mean(test1_data[epoch, ch, baseline_mask])
            test1_corrected[epoch, ch, :] = (test1_data[epoch, ch, :] - epoch_baseline_mean) + global_baseline_mean

        for epoch in range(n_epochs_test2):
            epoch_baseline_mean = np.mean(test2_data[epoch, ch, baseline_mask])
            test2_corrected[epoch, ch, :] = (test2_data[epoch, ch, :] - epoch_baseline_mean) + global_baseline_mean

        for epoch in range(n_epochs_test3):
            epoch_baseline_mean = np.mean(test3_data[epoch, ch, baseline_mask])
            test3_corrected[epoch, ch, :] = (test3_data[epoch, ch, :] - epoch_baseline_mean) + global_baseline_mean

    # 验证基线矫正效果
    test1_final_baseline = np.mean(test1_corrected[:, :, baseline_mask])
    test2_final_baseline = np.mean(test2_corrected[:, :, baseline_mask])
    test3_final_baseline = np.mean(test3_corrected[:, :, baseline_mask])

    baseline_diff_12 = abs(test1_final_baseline - test2_final_baseline) * 1e6
    baseline_diff_13 = abs(test1_final_baseline - test3_final_baseline) * 1e6
    baseline_diff_23 = abs(test2_final_baseline - test3_final_baseline) * 1e6

    print(f"  基线矫正验证:")
    print(f"    Test1基线均值: {test1_final_baseline*1e6:.3f} μV")
    print(f"    Test2基线均值: {test2_final_baseline*1e6:.3f} μV")
    print(f"    Test3基线均值: {test3_final_baseline*1e6:.3f} μV")
    print(f"    基线差异 Test1-Test2: {baseline_diff_12:.3f} μV")
    print(f"    基线差异 Test1-Test3: {baseline_diff_13:.3f} μV")
    print(f"    基线差异 Test2-Test3: {baseline_diff_23:.3f} μV")

    max_diff = max(baseline_diff_12, baseline_diff_13, baseline_diff_23)
    if max_diff > 0.1:
        print(f"    ⚠️ 警告: 最大基线差异较大 ({max_diff:.3f} μV > 0.1 μV)")
    else:
        print(f"    ✅ 基线矫正成功，所有差异在可接受范围内")

    return test1_corrected, test2_corrected, test3_corrected

def find_latest_files():
    """查找最新的三个刺激阶段数据文件（Test3使用优化后数据）"""
    test1_files = [f for f in os.listdir(DATA_DIR) if f.startswith('test1_raw_epochs_') and f.endswith('.h5')]
    test2_files = [f for f in os.listdir(DATA_DIR) if f.startswith('test2_raw_epochs_') and f.endswith('.h5')]

    # Test3使用优化后的数据
    test3_files = [f for f in os.listdir(OPTIMIZATION_DIR) if f.startswith('test3_optimized_epochs_') and f.endswith('.h5')]

    if not test1_files or not test2_files or not test3_files:
        missing = []
        if not test1_files: missing.append('test1')
        if not test2_files: missing.append('test2')
        if not test3_files: missing.append('test3 (optimized)')
        raise FileNotFoundError(f"未找到以下数据文件: {', '.join(missing)}")

    test1_files.sort()
    test2_files.sort()
    test3_files.sort()

    test1_file = os.path.join(DATA_DIR, test1_files[-1])
    test2_file = os.path.join(DATA_DIR, test2_files[-1])
    test3_file = os.path.join(OPTIMIZATION_DIR, test3_files[-1])  # 使用优化后的Test3数据

    print(f"使用的数据文件:")
    print(f"  Test1: {test1_files[-1]} (原始数据)")
    print(f"  Test2: {test2_files[-1]} (原始数据)")
    print(f"  Test3: {test3_files[-1]} (优化后数据)")

    return test1_file, test2_file, test3_file

def extract_region_data(data, ch_names, subject_ids, region_electrodes):
    """提取指定区域的电极数据"""
    valid_electrodes = [e for e in region_electrodes if e in ch_names]
    print(f"    实际存在的电极: {valid_electrodes}")

    if not valid_electrodes:
        return None, []

    ch_indices = [ch_names.index(e) for e in valid_electrodes]

    subject_data = {}
    unique_subjects = sorted(set(subject_ids))

    for subj in unique_subjects:
        subj_indices = [i for i, s in enumerate(subject_ids) if s == subj]
        if subj_indices:
            subj_data = data[subj_indices][:, ch_indices, :]
            subject_data[subj] = subj_data

    return subject_data, valid_electrodes

def calculate_region_average(subject_data):
    """计算区域平均波形"""
    if subject_data is None:
        return None

    all_data = []
    for subj_data in subject_data.values():
        subj_avg = np.mean(subj_data, axis=0)  # 对epochs求平均
        region_avg = np.mean(subj_avg, axis=0)  # 对电极求平均
        all_data.append(region_avg)

    grand_avg = np.mean(all_data, axis=0)
    return grand_avg

def calculate_optimal_y_range_three_phases(region_results):
    """动态计算最优的Y轴显示范围"""
    all_values = []

    for _, result in region_results.items():
        test1_avg = result['test1_avg']
        test2_avg = result['test2_avg']
        test3_avg = result['test3_avg']

        # 收集三个阶段的数值（转换为μV）
        all_values.extend(test1_avg * 1e6)
        all_values.extend(test2_avg * 1e6)
        all_values.extend(test3_avg * 1e6)

    if not all_values:
        return (-10, 10)  # 默认范围

    # 计算数据范围
    min_val = np.min(all_values)
    max_val = np.max(all_values)

    # 添加10%的边距以确保数据完全可见
    range_margin = (max_val - min_val) * 0.1
    y_min = min_val - range_margin
    y_max = max_val + range_margin

    print(f"\n📊 Y轴范围计算:")
    print(f"  数据最小值: {min_val:.1f} μV")
    print(f"  数据最大值: {max_val:.1f} μV")
    print(f"  显示范围: {y_min:.1f} 到 {y_max:.1f} μV")
    print(f"  范围跨度: {y_max - y_min:.1f} μV")

    return (y_min, y_max)

def create_three_phase_visualization(region_results, times):
    """创建三阶段刺激态对比可视化图表"""
    print("\n创建三阶段刺激态对比可视化图表...")

    # 动态计算最优Y轴范围
    y_min, y_max = calculate_optimal_y_range_three_phases(region_results)

    # 创建2:1宽高比的图形
    fig = plt.figure(figsize=(16, 8))

    # 设置总标题
    fig.suptitle('HEP三阶段刺激态对比分析：Test1 vs Test2 vs Test3\n'
                 '双侧乳突参考 (TP9/TP10) | 0.5-45Hz + 高斯平滑',
                 fontsize=14, fontweight='bold', y=0.95)

    # 创建子图布局：2行2列
    gs = fig.add_gridspec(2, 2, hspace=0.35, wspace=0.3)

    # 为每个脑区创建子图
    region_names = list(region_results.keys())

    for i, region_name in enumerate(region_names[:4]):  # 最多显示4个脑区
        result = region_results[region_name]
        test1_avg = result['test1_avg']
        test2_avg = result['test2_avg']
        test3_avg = result['test3_avg']

        # 子图位置
        row = i // 2
        col = i % 2
        ax = fig.add_subplot(gs[row, col])

        # 绘制三条波形线
        ax.plot(times * 1000, test1_avg * 1e6,
                color=PHASE_COLORS['Test1'], linewidth=1.0,
                label='Test1', alpha=0.95)
        ax.plot(times * 1000, test2_avg * 1e6,
                color=PHASE_COLORS['Test2'], linewidth=1.0,
                label='Test2', alpha=0.95)
        ax.plot(times * 1000, test3_avg * 1e6,
                color=PHASE_COLORS['Test3'], linewidth=1.0,
                label='Test3', alpha=0.95)

        # 设置坐标轴和标签
        ax.set_xlim(-200, 650)
        ax.set_ylim(y_min, y_max)
        ax.set_xlabel('时间 (ms)', fontsize=10)
        ax.set_ylabel('幅值 (μV)', fontsize=10)
        ax.set_title(f'{region_name}\n0.5-45Hz + 高斯平滑', fontsize=11, fontweight='bold')

        # 网格和零线
        ax.grid(True, alpha=0.3, linewidth=0.5)
        ax.axhline(0, color='black', linewidth=0.5, alpha=0.6)
        ax.axvline(0, color='gray', linewidth=0.5, alpha=0.5, linestyle='--')

        # 图例
        ax.legend(fontsize=9, loc='upper right', framealpha=0.95,
                 fancybox=True, shadow=True, edgecolor='black')

        # 电极信息标注
        electrode_text = f"电极: {', '.join(result['electrodes'])}"
        ax.text(0.02, 0.02, electrode_text, transform=ax.transAxes,
                fontsize=8, verticalalignment='bottom', horizontalalignment='left',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.9,
                         edgecolor='gray'))

    plt.tight_layout()

    # 保存图片
    output_file = os.path.join(RESULT_DIR, 'hep_analysis_test1_test2_test3_comparison_tp9tp10.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"三阶段刺激态对比图表已保存: {output_file}")

    plt.show()
    return fig

def main():
    """主函数：执行三阶段刺激态对比分析"""
    print("=" * 80)
    print("版本3 - HEP三阶段刺激态对比分析：Test1 vs Test2 vs Test3")
    print("双侧乳突参考 (TP9/TP10) | 使用优化后Test3数据")
    print("=" * 80)

    try:
        # 步骤1: 查找并加载数据文件
        print("\n步骤1: 加载数据文件")
        test1_file, test2_file, test3_file = find_latest_files()

        # 加载三个阶段的数据
        test1_data, ch_names, times, test1_subject_ids, sampling_freq = load_hep_data(test1_file)
        test2_data, _, _, test2_subject_ids, _ = load_hep_data(test2_file)
        test3_data, _, _, test3_subject_ids, _ = load_hep_data(test3_file)

        # 步骤2: 数据预处理
        print("\n步骤2: 数据预处理")
        print("  应用带通滤波...")
        test1_data = apply_bandpass_filter(test1_data, sampling_freq)
        test2_data = apply_bandpass_filter(test2_data, sampling_freq)
        test3_data = apply_bandpass_filter(test3_data, sampling_freq)

        # 步骤3: 统一基线矫正
        print("\n步骤3: 统一基线矫正")
        test1_data, test2_data, test3_data = apply_unified_baseline_correction_three_phases(
            test1_data, test2_data, test3_data, times)

        # 步骤4: 信号平滑
        print("\n步骤4: 信号平滑")
        test1_data = apply_gaussian_smoothing(test1_data)
        test2_data = apply_gaussian_smoothing(test2_data)
        test3_data = apply_gaussian_smoothing(test3_data)

        # 步骤5: 脑区分组分析
        print("\n步骤5: 脑区分组分析")
        region_results = {}

        for region_name, electrodes in BRAIN_REGIONS.items():
            print(f"\n分析 {region_name}:")

            # 提取各阶段的区域数据
            test1_subject_data, valid_electrodes = extract_region_data(
                test1_data, ch_names, test1_subject_ids, electrodes)
            test2_subject_data, _ = extract_region_data(
                test2_data, ch_names, test2_subject_ids, electrodes)
            test3_subject_data, _ = extract_region_data(
                test3_data, ch_names, test3_subject_ids, electrodes)

            if test1_subject_data is not None and test2_subject_data is not None and test3_subject_data is not None:
                # 计算区域平均波形
                test1_avg = calculate_region_average(test1_subject_data)
                test2_avg = calculate_region_average(test2_subject_data)
                test3_avg = calculate_region_average(test3_subject_data)

                region_results[region_name] = {
                    'test1_avg': test1_avg,
                    'test2_avg': test2_avg,
                    'test3_avg': test3_avg,
                    'electrodes': valid_electrodes
                }

                print(f"    ✅ 成功处理 {region_name}")
            else:
                print(f"    ❌ 跳过 {region_name}（数据不完整）")

        # 步骤6: 创建可视化图表
        if region_results:
            print(f"\n步骤6: 创建可视化图表")
            fig = create_three_phase_visualization(region_results, times)

            print(f"\n✅ 版本3 - 三阶段刺激态对比分析完成！")
            print(f"   处理的脑区数量: {len(region_results)}")
            print(f"   时间窗口: -200 到 650 ms")
            print(f"   滤波设置: 0.5-45 Hz + 高斯平滑")
            print(f"   配色方案: Test1(深橙色), Test2(深紫色), Test3(深棕色)")
        else:
            print("❌ 未能处理任何脑区数据")

    except Exception as e:
        print(f"❌ 分析过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
