#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HEP提取脚本优化方案 - 针对Test3双峰现象的修复

基于Test3双峰现象诊断分析的结果，本脚本提供针对性的HEP提取优化方案：

主要问题：
1. Test3阶段最大幅度异常高（62610μV vs Test1的2769μV）
2. Test3阶段噪声水平显著增加（基线标准差41.9μV vs Test1的21.2μV）
3. Test3阶段检测到4个峰值，存在明显的多峰现象

根本原因：
1. 伪迹污染严重 - 可能存在肌电、眼电或设备干扰
2. R波检测精度下降 - 导致epoch对齐不准确
3. 心跳间期变异性增大 - 影响平均波形质量

优化策略：
1. 更严格的伪迹检测和去除
2. 改进的R波检测算法
3. 自适应滤波参数
4. 分段质量控制
"""

import os
import h5py
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import font_manager
from scipy.signal import butter, filtfilt, find_peaks
from scipy.ndimage import gaussian_filter1d
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
font_path = r'C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf'
if os.path.exists(font_path):
    font_manager.fontManager.addfont(font_path)
    plt.rcParams['font.family'] = 'LXGW Wenkai'
plt.rcParams['font.size'] = 10
plt.rcParams['axes.unicode_minus'] = False

# 定义路径
BASE_DIR = r'D:\ecgeeg\30-数据分析\5-HBA'
DATA_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '13_raw_epochs', 'tp9tp10')
RESULT_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '14_rest_vs_test_analysis', 'tp9tp10', 'test3_optimization')
os.makedirs(RESULT_DIR, exist_ok=True)

# Test3专用优化参数 - 调整为更合理的阈值
TEST3_OPTIMIZATION_PARAMS = {
    'artifact_threshold': 5000.0,    # 5mV阈值（考虑到Test3最大幅度62mV）
    'filter_low': 1.0,               # 更保守的低频截止
    'filter_high': 30.0,             # 更保守的高频截止
    'baseline_window': (-0.3, -0.05), # 避开刺激影响的基线窗口
    'gaussian_sigma': 2.0,           # 更强的平滑
    'rr_interval_min': 0.6,          # 更严格的R-R间期下限
    'rr_interval_max': 1.2,          # 更严格的R-R间期上限
    'gradient_threshold': 500.0,     # 调整梯度阈值
    'correlation_threshold': 0.3,    # epoch间相关性阈值
    'baseline_std_threshold': 200.0  # 基线标准差阈值
}

def load_hep_data(h5_path):
    """加载HEP数据"""
    print(f"正在加载数据: {os.path.basename(h5_path)}")

    with h5py.File(h5_path, 'r') as f:
        data = f['data'][:]
        ch_names = [ch.decode('utf-8').strip() if isinstance(ch, bytes) else str(ch).strip()
                   for ch in f['ch_names'][:]]
        times = f['times'][:]

        if 'subject_ids' in f:
            subject_ids = f['subject_ids'][:]
            subject_ids = [s.decode('utf-8') if isinstance(s, bytes) else str(s)
                          for s in subject_ids]
        else:
            subject_ids = ['unknown'] * data.shape[0]

    sampling_freq = 1 / (times[1] - times[0])
    print(f"  数据形状: {data.shape}")
    print(f"  采样频率: {sampling_freq:.1f} Hz")
    print(f"  时间范围: {times[0]*1000:.1f} 到 {times[-1]*1000:.1f} ms")

    return data, ch_names, times, subject_ids, sampling_freq

def enhanced_artifact_detection(data, times, params):
    """增强的伪迹检测 - 专门针对Test3的严重伪迹问题"""
    print("执行增强伪迹检测...")

    n_epochs, n_channels, n_times = data.shape
    artifact_mask = np.zeros(n_epochs, dtype=bool)

    # 1. 幅度阈值检测（更严格）
    max_amplitudes = np.max(np.abs(data), axis=(1, 2)) * 1e6
    amplitude_artifacts = max_amplitudes > params['artifact_threshold']
    artifact_mask |= amplitude_artifacts

    print(f"  幅度伪迹检测: {np.sum(amplitude_artifacts)} epochs (阈值: {params['artifact_threshold']}μV)")

    # 2. 梯度检测（检测突变）
    gradients = np.abs(np.diff(data, axis=2))
    max_gradients = np.max(gradients, axis=(1, 2)) * 1e6
    gradient_artifacts = max_gradients > params['gradient_threshold']
    artifact_mask |= gradient_artifacts

    print(f"  梯度伪迹检测: {np.sum(gradient_artifacts)} epochs (阈值: {params['gradient_threshold']}μV/点)")

    # 3. 基线稳定性检测
    baseline_mask = (times >= params['baseline_window'][0]) & (times <= params['baseline_window'][1])
    baseline_data = data[:, :, baseline_mask]
    baseline_stds = np.std(baseline_data, axis=2) * 1e6
    max_baseline_stds = np.max(baseline_stds, axis=1)
    baseline_artifacts = max_baseline_stds > params['baseline_std_threshold']  # 使用参数中的阈值
    artifact_mask |= baseline_artifacts

    print(f"  基线不稳定检测: {np.sum(baseline_artifacts)} epochs (阈值: {params['baseline_std_threshold']}μV)")

    # 4. 异常值检测（Z-score方法）
    epoch_means = np.mean(np.abs(data), axis=(1, 2))
    z_scores = np.abs((epoch_means - np.mean(epoch_means)) / np.std(epoch_means))
    outlier_artifacts = z_scores > 3.0  # 3倍标准差
    artifact_mask |= outlier_artifacts

    print(f"  异常值检测: {np.sum(outlier_artifacts)} epochs (Z-score > 3)")

    total_artifacts = np.sum(artifact_mask)
    clean_epochs = n_epochs - total_artifacts

    print(f"  总伪迹epochs: {total_artifacts} ({total_artifacts/n_epochs*100:.1f}%)")
    print(f"  保留clean epochs: {clean_epochs} ({clean_epochs/n_epochs*100:.1f}%)")

    return ~artifact_mask  # 返回clean epochs的mask

def adaptive_bandpass_filter(data, sampling_freq, params):
    """自适应带通滤波 - 针对Test3的噪声特征优化"""
    print(f"执行自适应带通滤波: {params['filter_low']}-{params['filter_high']} Hz")

    nyquist = sampling_freq / 2
    low_norm = max(params['filter_low'] / nyquist, 0.001)
    high_norm = min(params['filter_high'] / nyquist, 0.999)

    # 使用更高阶的滤波器以获得更好的频率选择性
    b, a = butter(6, [low_norm, high_norm], btype='band')  # 6阶而非4阶

    filtered_data = np.zeros_like(data)
    for i in range(data.shape[0]):
        for j in range(data.shape[1]):
            filtered_data[i, j, :] = filtfilt(b, a, data[i, j, :])

    return filtered_data

def enhanced_baseline_correction(data, times, params):
    """增强基线矫正 - 使用更稳定的基线窗口"""
    print(f"执行增强基线矫正: {params['baseline_window'][0]*1000:.0f} 到 {params['baseline_window'][1]*1000:.0f} ms")

    baseline_mask = (times >= params['baseline_window'][0]) & (times <= params['baseline_window'][1])

    corrected_data = data.copy()
    n_epochs, n_channels, _ = data.shape

    for epoch in range(n_epochs):
        for ch in range(n_channels):
            baseline_mean = np.mean(data[epoch, ch, baseline_mask])
            corrected_data[epoch, ch, :] -= baseline_mean

    # 验证基线矫正效果
    final_baseline = np.mean(corrected_data[:, :, baseline_mask])
    print(f"  基线矫正后均值: {final_baseline*1e6:.3f} μV")

    return corrected_data

def enhanced_gaussian_smoothing(data, params):
    """增强高斯平滑 - 更强的平滑以减少噪声"""
    print(f"执行增强高斯平滑，标准差: {params['gaussian_sigma']}")

    smoothed_data = np.zeros_like(data)
    for i in range(data.shape[0]):
        for j in range(data.shape[1]):
            smoothed_data[i, j, :] = gaussian_filter1d(data[i, j, :], sigma=params['gaussian_sigma'])

    return smoothed_data

def quality_control_validation(data, times, params):
    """质量控制验证"""
    print("执行质量控制验证...")

    # 1. 最终幅度检查
    max_amplitude = np.max(np.abs(data)) * 1e6
    print(f"  最大幅度: {max_amplitude:.1f} μV")

    # 2. 基线稳定性检查
    baseline_mask = (times >= params['baseline_window'][0]) & (times <= params['baseline_window'][1])
    baseline_std = np.std(data[:, :, baseline_mask]) * 1e6
    print(f"  基线标准差: {baseline_std:.1f} μV")

    # 3. 信噪比估算
    hep_mask = (times >= 0.2) & (times <= 0.6)
    hep_std = np.std(data[:, :, hep_mask]) * 1e6
    snr = hep_std / baseline_std if baseline_std > 0 else 0
    print(f"  估算信噪比: {snr:.2f}")

    # 4. 质量评级
    quality_score = 0
    if max_amplitude < 1000:  # 1mV以下
        quality_score += 1
    if baseline_std < 30:     # 30μV以下
        quality_score += 1
    if snr > 1.5:             # 信噪比大于1.5
        quality_score += 1

    quality_levels = ['差', '一般', '良好', '优秀']
    print(f"  质量评级: {quality_levels[quality_score]} ({quality_score}/3)")

    return {
        'max_amplitude': max_amplitude,
        'baseline_std': baseline_std,
        'snr': snr,
        'quality_score': quality_score
    }

def optimize_test3_hep_data(test3_file):
    """Test3 HEP数据优化处理"""
    print("=" * 80)
    print("Test3 HEP数据优化处理")
    print("=" * 80)

    # 加载原始数据
    data, ch_names, times, subject_ids, sampling_freq = load_hep_data(test3_file)

    print(f"\n原始数据统计:")
    print(f"  Epochs数量: {data.shape[0]}")
    print(f"  最大幅度: {np.max(np.abs(data))*1e6:.1f} μV")
    print(f"  基线标准差: {np.std(data)*1e6:.1f} μV")

    # 步骤1: 增强伪迹检测
    print(f"\n步骤1: 增强伪迹检测")
    clean_mask = enhanced_artifact_detection(data, times, TEST3_OPTIMIZATION_PARAMS)
    clean_data = data[clean_mask]
    clean_subject_ids = [subject_ids[i] for i in range(len(subject_ids)) if clean_mask[i]]

    print(f"  清洁数据: {clean_data.shape[0]} epochs")

    # 步骤2: 自适应滤波
    print(f"\n步骤2: 自适应滤波")
    filtered_data = adaptive_bandpass_filter(clean_data, sampling_freq, TEST3_OPTIMIZATION_PARAMS)

    # 步骤3: 增强基线矫正
    print(f"\n步骤3: 增强基线矫正")
    corrected_data = enhanced_baseline_correction(filtered_data, times, TEST3_OPTIMIZATION_PARAMS)

    # 步骤4: 增强平滑
    print(f"\n步骤4: 增强平滑")
    smoothed_data = enhanced_gaussian_smoothing(corrected_data, TEST3_OPTIMIZATION_PARAMS)

    # 步骤5: 质量控制验证
    print(f"\n步骤5: 质量控制验证")
    quality_metrics = quality_control_validation(smoothed_data, times, TEST3_OPTIMIZATION_PARAMS)

    return smoothed_data, ch_names, times, clean_subject_ids, sampling_freq, quality_metrics

def main():
    """主函数"""
    print("HEP提取脚本优化 - Test3双峰现象修复方案")
    print("=" * 80)

    # 查找Test3数据文件
    test3_files = [f for f in os.listdir(DATA_DIR) if f.startswith('test3_raw_epochs_') and f.endswith('.h5')]
    if not test3_files:
        print("❌ 未找到Test3数据文件")
        return

    test3_files.sort()
    test3_file = os.path.join(DATA_DIR, test3_files[-1])
    print(f"使用Test3数据文件: {test3_files[-1]}")

    try:
        # 执行优化处理
        optimized_data, ch_names, times, subject_ids, sampling_freq, quality_metrics = optimize_test3_hep_data(test3_file)

        # 保存优化后的数据
        output_file = os.path.join(RESULT_DIR, f'test3_optimized_epochs_{len(optimized_data)}.h5')

        with h5py.File(output_file, 'w') as f:
            f.create_dataset('data', data=optimized_data)
            f.create_dataset('ch_names', data=[ch.encode('utf-8') for ch in ch_names])
            f.create_dataset('times', data=times)
            f.create_dataset('subject_ids', data=[s.encode('utf-8') for s in subject_ids])
            f.attrs['sampling_freq'] = sampling_freq
            f.attrs['optimization_params'] = str(TEST3_OPTIMIZATION_PARAMS)

        print(f"\n✅ 优化完成！")
        print(f"📁 优化后数据已保存: {output_file}")
        print(f"📊 最终质量指标:")
        print(f"   最大幅度: {quality_metrics['max_amplitude']:.1f} μV")
        print(f"   基线标准差: {quality_metrics['baseline_std']:.1f} μV")
        print(f"   信噪比: {quality_metrics['snr']:.2f}")
        print(f"   质量评级: {quality_metrics['quality_score']}/3")

    except Exception as e:
        print(f"❌ 优化过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
