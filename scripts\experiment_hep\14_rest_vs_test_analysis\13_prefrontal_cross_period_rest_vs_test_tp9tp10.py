#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
版本6 - 跨期静息态vs刺激态对比 (test1 vs rest2, test2 vs rest3) - 统一预处理标准

本脚本分析：
1. 对比相邻时期的刺激态和静息态
2. 两个子图：test1 vs rest2、test2 vs rest3
3. 观察刺激后恢复期的压力状态变化
4. 使用与其他版本一致的预处理参数（0.5-45Hz滤波 + 高斯平滑）

配色方案：刺激态(橙色系)、恢复期静息态(绿色系)
数据来源：Test1/Test2/Rest2/Rest3原始数据
"""

import os
import h5py
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import font_manager
from scipy.signal import butter, filtfilt
from scipy.ndimage import gaussian_filter1d
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
font_path = r'C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf'
if os.path.exists(font_path):
    font_manager.fontManager.addfont(font_path)
    plt.rcParams['font.family'] = 'LXGW Wenkai'
plt.rcParams['font.size'] = 10
plt.rcParams['axes.unicode_minus'] = False

# 定义路径
BASE_DIR = r'D:\ecgeeg\30-数据分析\5-HBA'
DATA_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '13_raw_epochs', 'tp9tp10')
RESULT_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '14_rest_vs_test_analysis', 'tp9tp10')
os.makedirs(RESULT_DIR, exist_ok=True)

# 定义脑区电极分组
BRAIN_REGIONS = {
    '左前额叶区域': ['Fp1', 'AF3', 'AF7', 'F3', 'F5'],
    '右前额叶区域': ['Fp2', 'AF4', 'AF8', 'F4', 'F6'],
    '额中央区域': ['Fpz', 'AFz', 'Fz', 'FCz'],
    '中央区域': ['Cz', 'CPz', 'Pz']
}

# 时间窗口设置
BASELINE_WINDOW = (-0.2, 0.0)  # 基线矫正窗口 -200ms到0ms

# 配色方案
CROSS_COLORS = {
    'Test': '#FF8C00',   # 深橙色
    'Rest': '#228B22'    # 森林绿
}

def load_hep_data(h5_path, max_epochs=3000):
    """加载HEP数据，对大数据集进行智能采样"""
    print(f"正在加载数据: {os.path.basename(h5_path)}")

    with h5py.File(h5_path, 'r') as f:
        data = f['data'][:]
        ch_names = [ch.decode('utf-8').strip() if isinstance(ch, bytes) else str(ch).strip()
                   for ch in f['ch_names'][:]]
        times = f['times'][:]

        if 'subject_ids' in f:
            subject_ids = f['subject_ids'][:]
            subject_ids = [s.decode('utf-8') if isinstance(s, bytes) else str(s)
                          for s in subject_ids]
        else:
            subject_ids = ['unknown'] * data.shape[0]

    # 智能采样：如果数据量过大，进行随机采样
    n_epochs = data.shape[0]
    if n_epochs > max_epochs:
        print(f"  数据量较大 ({n_epochs} epochs)，进行随机采样到 {max_epochs} epochs")
        np.random.seed(42)  # 确保可重复性
        sample_indices = np.random.choice(n_epochs, max_epochs, replace=False)
        sample_indices = np.sort(sample_indices)  # 保持时间顺序

        data = data[sample_indices]
        subject_ids = [subject_ids[i] for i in sample_indices]
        print(f"  ✅ 采样完成，保留 {len(sample_indices)} 个高质量epochs")

    sampling_freq = 1 / (times[1] - times[0])
    print(f"  最终数据形状: {data.shape}")
    print(f"  采样频率: {sampling_freq:.1f} Hz")

    return data, ch_names, times, subject_ids, sampling_freq

def apply_bandpass_filter(data, sampling_freq, low_freq=0.5, high_freq=45):
    """应用带通滤波器"""
    print(f"  应用带通滤波: {low_freq}-{high_freq} Hz")

    nyquist = sampling_freq / 2
    low_norm = max(low_freq / nyquist, 0.001)
    high_norm = min(high_freq / nyquist, 0.999)

    b, a = butter(4, [low_norm, high_norm], btype='band')

    filtered_data = np.zeros_like(data)
    for i in range(data.shape[0]):
        for j in range(data.shape[1]):
            filtered_data[i, j, :] = filtfilt(b, a, data[i, j, :])

    return filtered_data

def apply_gaussian_smoothing(data, sigma=1.5):
    """应用高斯平滑"""
    print(f"  应用高斯平滑，标准差: {sigma}")

    smoothed_data = np.zeros_like(data)
    for i in range(data.shape[0]):
        for j in range(data.shape[1]):
            smoothed_data[i, j, :] = gaussian_filter1d(data[i, j, :], sigma=sigma)

    return smoothed_data

def apply_baseline_correction(data, times):
    """基线矫正"""
    baseline_mask = (times >= BASELINE_WINDOW[0]) & (times <= BASELINE_WINDOW[1])
    baseline_mean = np.mean(data[..., baseline_mask], axis=-1, keepdims=True)
    return data - baseline_mean

def find_latest_files():
    """查找最新的数据文件"""
    rest2_files = [f for f in os.listdir(DATA_DIR) if f.startswith('rest2_raw_epochs_') and f.endswith('.h5')]
    rest3_files = [f for f in os.listdir(DATA_DIR) if f.startswith('rest3_raw_epochs_') and f.endswith('.h5')]
    test1_files = [f for f in os.listdir(DATA_DIR) if f.startswith('test1_raw_epochs_') and f.endswith('.h5')]
    test2_files = [f for f in os.listdir(DATA_DIR) if f.startswith('test2_raw_epochs_') and f.endswith('.h5')]

    files = {
        'rest2': rest2_files[-1] if rest2_files else None,
        'rest3': rest3_files[-1] if rest3_files else None,
        'test1': test1_files[-1] if test1_files else None,
        'test2': test2_files[-1] if test2_files else None,
    }

    print(f"使用的数据文件:")
    for phase, filename in files.items():
        if filename:
            print(f"  {phase}: {filename}")

    return {phase: os.path.join(DATA_DIR, filename) if filename else None
            for phase, filename in files.items()}

def extract_region_data(data, ch_names, subject_ids, region_electrodes):
    """提取指定区域的电极数据"""
    valid_electrodes = [e for e in region_electrodes if e in ch_names]

    if not valid_electrodes:
        return None, []

    ch_indices = [ch_names.index(e) for e in valid_electrodes]

    subject_data = {}
    unique_subjects = sorted(set(subject_ids))

    for subj in unique_subjects:
        subj_indices = [i for i, s in enumerate(subject_ids) if s == subj]
        if subj_indices:
            subj_data = data[subj_indices][:, ch_indices, :]
            subject_data[subj] = subj_data

    return subject_data, valid_electrodes

def calculate_region_average(subject_data):
    """计算区域平均波形"""
    if subject_data is None:
        return None

    all_data = []
    for subj_data in subject_data.values():
        subj_avg = np.mean(subj_data, axis=0)  # 对epochs求平均
        region_avg = np.mean(subj_avg, axis=0)  # 对电极求平均
        all_data.append(region_avg)

    grand_avg = np.mean(all_data, axis=0)
    return grand_avg

def calculate_optimal_y_range_cross_period(region_results):
    """动态计算跨期对比的最优Y轴显示范围"""
    all_values = []

    for phase, phase_results in region_results.items():
        for region_name, result in phase_results.items():
            avg_data = result['avg']
            all_values.extend(avg_data * 1e6)

    if not all_values:
        return (-10, 10)  # 默认范围

    # 计算数据范围
    min_val = np.min(all_values)
    max_val = np.max(all_values)

    # 添加10%的边距以确保数据完全可见
    range_margin = (max_val - min_val) * 0.1
    y_min = min_val - range_margin
    y_max = max_val + range_margin

    print(f"\n📊 跨期对比Y轴范围计算:")
    print(f"  数据最小值: {min_val:.1f} μV")
    print(f"  数据最大值: {max_val:.1f} μV")
    print(f"  显示范围: {y_min:.1f} 到 {y_max:.1f} μV")
    print(f"  范围跨度: {y_max - y_min:.1f} μV")

    return (y_min, y_max)

def create_cross_period_visualization(region_results, times):
    """创建跨期静息态vs刺激态对比可视化图表"""
    print("\n创建跨期静息态vs刺激态对比可视化图表...")

    # 动态计算最优Y轴范围
    y_min, y_max = calculate_optimal_y_range_cross_period(region_results)

    # 创建2:1宽高比的图形（2个子图）
    fig = plt.figure(figsize=(16, 8))

    # 设置总标题
    fig.suptitle('HEP跨期静息态vs刺激态对比分析\n'
                 '双侧乳突参考 (TP9/TP10) | 0.5-45Hz + 高斯平滑',
                 fontsize=14, fontweight='bold', y=0.95)

    # 创建子图布局：1行2列
    gs = fig.add_gridspec(1, 2, hspace=0.3, wspace=0.3)

    cross_pairs = [('test1', 'rest2'), ('test2', 'rest3')]
    titles = ['Test1 vs Rest2 (刺激后恢复)', 'Test2 vs Rest3 (刺激后恢复)']

    for i, ((test_phase, rest_phase), title) in enumerate(zip(cross_pairs, titles)):
        if test_phase in region_results and rest_phase in region_results:
            ax = fig.add_subplot(gs[0, i])

            # 使用额中央区域作为代表
            region_name = '额中央区域'
            if region_name in region_results[test_phase] and region_name in region_results[rest_phase]:
                test_avg = region_results[test_phase][region_name]['avg']
                rest_avg = region_results[rest_phase][region_name]['avg']

                # 绘制波形线
                ax.plot(times * 1000, test_avg * 1e6,
                        color=CROSS_COLORS['Test'], linewidth=1.0,
                        label=f'{test_phase.capitalize()} (刺激态)', alpha=0.95)
                ax.plot(times * 1000, rest_avg * 1e6,
                        color=CROSS_COLORS['Rest'], linewidth=1.0,
                        label=f'{rest_phase.capitalize()} (恢复期)', alpha=0.95)

                # 设置坐标轴和标签
                ax.set_xlim(-200, 650)
                ax.set_ylim(y_min, y_max)  # 动态范围基于实际数据
                ax.set_xlabel('时间 (ms)', fontsize=10)
                ax.set_ylabel('幅值 (μV)', fontsize=10)
                ax.set_title(title, fontsize=11, fontweight='bold')

                # 网格和零线
                ax.grid(True, alpha=0.3, linewidth=0.5)
                ax.axhline(0, color='black', linewidth=0.5, alpha=0.6)
                ax.axvline(0, color='gray', linewidth=0.5, alpha=0.5, linestyle='--')

                # 图例
                ax.legend(fontsize=9, loc='upper right', framealpha=0.95)

    plt.tight_layout()

    # 保存图片
    output_file = os.path.join(RESULT_DIR, 'hep_analysis_cross_period_rest_vs_test_tp9tp10.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"跨期对比图表已保存: {output_file}")

    plt.show()
    return fig

def main():
    """主函数：执行跨期静息态vs刺激态对比分析"""
    print("=" * 80)
    print("版本6 - HEP跨期静息态vs刺激态对比分析")
    print("双侧乳突参考 (TP9/TP10) | 统一预处理标准")
    print("=" * 80)

    try:
        # 步骤1: 查找并加载数据文件
        print("\n步骤1: 加载数据文件")
        file_paths = find_latest_files()

        region_results = {}

        # 加载并处理每个阶段的数据
        for phase, file_path in file_paths.items():
            if file_path and os.path.exists(file_path):
                print(f"\n处理 {phase}:")

                # 加载数据
                data, ch_names, times, subject_ids, sampling_freq = load_hep_data(file_path)

                # 数据预处理
                print("  数据预处理...")
                data = apply_bandpass_filter(data, sampling_freq)
                data = apply_baseline_correction(data, times)
                data = apply_gaussian_smoothing(data)

                # 脑区分析
                phase_results = {}
                for region_name, electrodes in BRAIN_REGIONS.items():
                    subject_data, valid_electrodes = extract_region_data(
                        data, ch_names, subject_ids, electrodes)

                    if subject_data is not None:
                        region_avg = calculate_region_average(subject_data)
                        phase_results[region_name] = {
                            'avg': region_avg,
                            'electrodes': valid_electrodes
                        }

                region_results[phase] = phase_results
                print(f"  ✅ 成功处理 {phase}")

        # 步骤2: 创建可视化图表
        if region_results:
            print(f"\n步骤2: 创建可视化图表")
            fig = create_cross_period_visualization(region_results, times)

            print(f"\n✅ 版本6 - 跨期静息态vs刺激态对比分析完成！")
            print(f"   处理的阶段数量: {len(region_results)}")
            print(f"   时间窗口: -200 到 650 ms")
            print(f"   滤波设置: 0.5-45 Hz + 高斯平滑")
            print(f"   配色方案: 刺激态(深橙色), 恢复期(森林绿)")
        else:
            print("❌ 未能处理任何数据")

    except Exception as e:
        print(f"❌ 分析过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
