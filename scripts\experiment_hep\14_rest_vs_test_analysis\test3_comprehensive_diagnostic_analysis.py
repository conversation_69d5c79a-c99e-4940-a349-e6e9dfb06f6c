#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test3双峰现象综合诊断分析脚本

本脚本提供Test3阶段双峰现象的全面诊断分析：
1. Test3与Test1、Test2的详细数据质量对比
2. Test3的R波检测精度和心跳间期变异性深度分析
3. Test3的基线稳定性和信号噪声水平评估
4. Test3的刺激时间对齐和epoch提取过程验证
5. HEP提取脚本的问题定位和修复方案
6. 生成完整的诊断报告和可视化图表

分析目标：
- 确认Test3双峰现象的存在
- 识别根本原因（数据质量vs处理算法）
- 提供针对性的解决方案
- 更新整体的Test1-2-3对比分析逻辑
"""

import os
import h5py
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import font_manager
import pandas as pd
from scipy.signal import find_peaks, butter, filtfilt
from scipy.ndimage import gaussian_filter1d
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
font_path = r'C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf'
if os.path.exists(font_path):
    font_manager.fontManager.addfont(font_path)
    plt.rcParams['font.family'] = 'LXGW Wenkai'
plt.rcParams['font.size'] = 10
plt.rcParams['axes.unicode_minus'] = False

# 定义路径
BASE_DIR = r'D:\ecgeeg\30-数据分析\5-HBA'
DATA_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '13_raw_epochs', 'tp9tp10')
RESULT_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '14_rest_vs_test_analysis', 'tp9tp10', 'test3_diagnostic')
os.makedirs(RESULT_DIR, exist_ok=True)

# 定义脑区电极分组
BRAIN_REGIONS = {
    '左前额叶区域': ['Fp1', 'AF3', 'AF7', 'F3', 'F5'],
    '右前额叶区域': ['Fp2', 'AF4', 'AF8', 'F4', 'F6'],
    '额中央区域': ['Fpz', 'AFz', 'Fz', 'FCz'],
    '中央区域': ['Cz', 'CPz', 'Pz']
}

# 时间窗口设置
BASELINE_WINDOW = (-0.2, 0.0)  # 基线矫正窗口 -200ms到0ms
HEP_WINDOW = (0.2, 0.6)        # HEP分析窗口 200-600ms

# 诊断标准
DIAGNOSTIC_THRESHOLDS = {
    'max_amplitude_ratio': 10.0,      # 最大幅度比值阈值
    'noise_level_ratio': 2.0,         # 噪声水平比值阈值
    'baseline_std_threshold': 50.0,   # 基线标准差阈值 (μV)
    'peak_detection_threshold': 0.3,  # 峰值检测阈值
    'correlation_threshold': 0.1      # epoch间相关性阈值
}

def load_hep_data(h5_path):
    """加载HEP数据"""
    print(f"正在加载数据: {os.path.basename(h5_path)}")

    with h5py.File(h5_path, 'r') as f:
        data = f['data'][:]
        ch_names = [ch.decode('utf-8').strip() if isinstance(ch, bytes) else str(ch).strip()
                   for ch in f['ch_names'][:]]
        times = f['times'][:]

        if 'subject_ids' in f:
            subject_ids = f['subject_ids'][:]
            subject_ids = [s.decode('utf-8') if isinstance(s, bytes) else str(s)
                          for s in subject_ids]
        else:
            subject_ids = ['unknown'] * data.shape[0]

    sampling_freq = 1 / (times[1] - times[0])
    print(f"  数据形状: {data.shape}")
    print(f"  采样频率: {sampling_freq:.1f} Hz")
    print(f"  时间范围: {times[0]*1000:.1f} 到 {times[-1]*1000:.1f} ms")

    return data, ch_names, times, subject_ids, sampling_freq

def apply_bandpass_filter(data, sampling_freq, low_freq=0.5, high_freq=45):
    """应用带通滤波器"""
    print(f"  应用带通滤波: {low_freq}-{high_freq} Hz")

    nyquist = sampling_freq / 2
    low_norm = max(low_freq / nyquist, 0.001)
    high_norm = min(high_freq / nyquist, 0.999)

    b, a = butter(4, [low_norm, high_norm], btype='band')

    filtered_data = np.zeros_like(data)
    for i in range(data.shape[0]):
        for j in range(data.shape[1]):
            filtered_data[i, j, :] = filtfilt(b, a, data[i, j, :])

    return filtered_data

def apply_gaussian_smoothing(data, sigma=1.5):
    """应用高斯平滑"""
    print(f"  应用高斯平滑，标准差: {sigma}")

    smoothed_data = np.zeros_like(data)
    for i in range(data.shape[0]):
        for j in range(data.shape[1]):
            smoothed_data[i, j, :] = gaussian_filter1d(data[i, j, :], sigma=sigma)

    return smoothed_data

def apply_unified_baseline_correction_three_phases(test1_data, test2_data, test3_data, times):
    """三阶段测试态统一基线矫正"""
    print("执行三阶段测试态统一基线矫正...")

    baseline_mask = (times >= BASELINE_WINDOW[0]) & (times <= BASELINE_WINDOW[1])
    print(f"  基线窗口: {BASELINE_WINDOW[0]*1000:.0f} 到 {BASELINE_WINDOW[1]*1000:.0f} ms")

    test1_corrected = test1_data.copy()
    test2_corrected = test2_data.copy()
    test3_corrected = test3_data.copy()

    n_epochs_test1, n_channels, _ = test1_data.shape
    n_epochs_test2 = test2_data.shape[0]
    n_epochs_test3 = test3_data.shape[0]

    print(f"  处理 Test1: {n_epochs_test1} epochs, Test2: {n_epochs_test2} epochs, Test3: {n_epochs_test3} epochs")

    # 为每个电极计算全局基线参考
    for ch in range(n_channels):
        test1_baseline_all = test1_data[:, ch, baseline_mask]
        test2_baseline_all = test2_data[:, ch, baseline_mask]
        test3_baseline_all = test3_data[:, ch, baseline_mask]

        all_baseline_data = np.concatenate([test1_baseline_all.flatten(),
                                          test2_baseline_all.flatten(),
                                          test3_baseline_all.flatten()])
        global_baseline_mean = np.mean(all_baseline_data)

        # 对每个epoch进行基线矫正
        for epoch in range(n_epochs_test1):
            epoch_baseline_mean = np.mean(test1_data[epoch, ch, baseline_mask])
            test1_corrected[epoch, ch, :] = (test1_data[epoch, ch, :] - epoch_baseline_mean) + global_baseline_mean

        for epoch in range(n_epochs_test2):
            epoch_baseline_mean = np.mean(test2_data[epoch, ch, baseline_mask])
            test2_corrected[epoch, ch, :] = (test2_data[epoch, ch, :] - epoch_baseline_mean) + global_baseline_mean

        for epoch in range(n_epochs_test3):
            epoch_baseline_mean = np.mean(test3_data[epoch, ch, baseline_mask])
            test3_corrected[epoch, ch, :] = (test3_data[epoch, ch, :] - epoch_baseline_mean) + global_baseline_mean

    # 验证基线矫正效果
    test1_final_baseline = np.mean(test1_corrected[:, :, baseline_mask])
    test2_final_baseline = np.mean(test2_corrected[:, :, baseline_mask])
    test3_final_baseline = np.mean(test3_corrected[:, :, baseline_mask])

    baseline_diff_12 = abs(test1_final_baseline - test2_final_baseline) * 1e6
    baseline_diff_13 = abs(test1_final_baseline - test3_final_baseline) * 1e6
    baseline_diff_23 = abs(test2_final_baseline - test3_final_baseline) * 1e6

    print(f"  基线矫正验证:")
    print(f"    Test1基线均值: {test1_final_baseline*1e6:.3f} μV")
    print(f"    Test2基线均值: {test2_final_baseline*1e6:.3f} μV")
    print(f"    Test3基线均值: {test3_final_baseline*1e6:.3f} μV")
    print(f"    基线差异 Test1-Test2: {baseline_diff_12:.3f} μV")
    print(f"    基线差异 Test1-Test3: {baseline_diff_13:.3f} μV")
    print(f"    基线差异 Test2-Test3: {baseline_diff_23:.3f} μV")

    max_diff = max(baseline_diff_12, baseline_diff_13, baseline_diff_23)
    if max_diff > 0.1:
        print(f"    ⚠️ 警告: 最大基线差异较大 ({max_diff:.3f} μV > 0.1 μV)")
    else:
        print(f"    ✅ 基线矫正成功，所有差异在可接受范围内")

    return test1_corrected, test2_corrected, test3_corrected

def find_latest_files():
    """查找最新的三个测试阶段数据文件"""
    test1_files = [f for f in os.listdir(DATA_DIR) if f.startswith('test1_raw_epochs_') and f.endswith('.h5')]
    test2_files = [f for f in os.listdir(DATA_DIR) if f.startswith('test2_raw_epochs_') and f.endswith('.h5')]
    test3_files = [f for f in os.listdir(DATA_DIR) if f.startswith('test3_raw_epochs_') and f.endswith('.h5')]

    if not test1_files or not test2_files or not test3_files:
        missing = []
        if not test1_files: missing.append('test1')
        if not test2_files: missing.append('test2')
        if not test3_files: missing.append('test3')
        raise FileNotFoundError(f"未找到以下数据文件: {', '.join(missing)}")

    test1_files.sort()
    test2_files.sort()
    test3_files.sort()

    test1_file = os.path.join(DATA_DIR, test1_files[-1])
    test2_file = os.path.join(DATA_DIR, test2_files[-1])
    test3_file = os.path.join(DATA_DIR, test3_files[-1])

    print(f"使用的数据文件:")
    print(f"  Test1: {test1_files[-1]}")
    print(f"  Test2: {test2_files[-1]}")
    print(f"  Test3: {test3_files[-1]}")

    return test1_file, test2_file, test3_file

def comprehensive_test3_analysis(test1_data, test2_data, test3_data, times, ch_names, sampling_freq):
    """Test3双峰现象综合分析"""
    print("\n" + "=" * 80)
    print("Test3双峰现象综合诊断分析")
    print("=" * 80)

    analysis_results = {
        'summary': {},
        'data_quality': {},
        'double_peak_analysis': {},
        'technical_diagnosis': {},
        'recommendations': []
    }

    # 1. 基础数据统计
    print("\n1. 基础数据统计分析")
    print("-" * 40)

    epoch_counts = {
        'test1': test1_data.shape[0],
        'test2': test2_data.shape[0],
        'test3': test3_data.shape[0]
    }

    print(f"Test1 epochs数量: {epoch_counts['test1']}")
    print(f"Test2 epochs数量: {epoch_counts['test2']}")
    print(f"Test3 epochs数量: {epoch_counts['test3']}")

    analysis_results['summary']['epoch_counts'] = epoch_counts

    # 2. 信号质量深度分析
    print("\n2. 信号质量深度分析")
    print("-" * 40)

    baseline_mask = (times >= BASELINE_WINDOW[0]) & (times <= BASELINE_WINDOW[1])
    hep_mask = (times >= HEP_WINDOW[0]) & (times <= HEP_WINDOW[1])

    for phase_name, data in [('Test1', test1_data), ('Test2', test2_data), ('Test3', test3_data)]:
        # 计算信号统计特征
        max_amplitude = np.max(np.abs(data)) * 1e6
        mean_amplitude = np.mean(np.abs(data)) * 1e6
        std_amplitude = np.std(data) * 1e6

        # 基线稳定性分析
        baseline_data = data[:, :, baseline_mask]
        baseline_std = np.std(baseline_data) * 1e6
        baseline_mean = np.mean(baseline_data) * 1e6

        # HEP窗口信号分析
        hep_data = data[:, :, hep_mask]
        hep_std = np.std(hep_data) * 1e6
        hep_mean = np.mean(hep_data) * 1e6

        # 信噪比估算
        snr = hep_std / baseline_std if baseline_std > 0 else 0

        quality_metrics = {
            'max_amplitude': max_amplitude,
            'mean_amplitude': mean_amplitude,
            'std_amplitude': std_amplitude,
            'baseline_std': baseline_std,
            'baseline_mean': baseline_mean,
            'hep_std': hep_std,
            'hep_mean': hep_mean,
            'snr': snr
        }

        analysis_results['data_quality'][phase_name.lower()] = quality_metrics

        print(f"{phase_name}:")
        print(f"  最大幅度: {max_amplitude:.2f} μV")
        print(f"  平均幅度: {mean_amplitude:.2f} μV")
        print(f"  信号标准差: {std_amplitude:.2f} μV")
        print(f"  基线标准差: {baseline_std:.2f} μV")
        print(f"  HEP窗口标准差: {hep_std:.2f} μV")
        print(f"  估算信噪比: {snr:.2f}")

        # 质量问题检测
        if max_amplitude > 10000:  # 10mV阈值
            print(f"  ⚠️ 警告: 最大幅度异常高 ({max_amplitude:.0f} μV)")
            analysis_results['recommendations'].append(f"{phase_name}: 检查伪迹去除，最大幅度过高")

        if baseline_std > DIAGNOSTIC_THRESHOLDS['baseline_std_threshold']:
            print(f"  ⚠️ 警告: 基线不稳定 ({baseline_std:.1f} μV)")
            analysis_results['recommendations'].append(f"{phase_name}: 基线不稳定，需要改进基线矫正")

        if snr < 1.0:
            print(f"  ⚠️ 警告: 信噪比过低 ({snr:.2f})")
            analysis_results['recommendations'].append(f"{phase_name}: 信噪比低，需要改进滤波或增加数据量")

        print()

    # 3. Test3异常检测
    print("\n3. Test3异常检测")
    print("-" * 40)

    test1_quality = analysis_results['data_quality']['test1']
    test2_quality = analysis_results['data_quality']['test2']
    test3_quality = analysis_results['data_quality']['test3']

    # 检测Test3相对于Test1和Test2的异常
    max_amp_ratio_13 = test3_quality['max_amplitude'] / test1_quality['max_amplitude']
    max_amp_ratio_23 = test3_quality['max_amplitude'] / test2_quality['max_amplitude']

    noise_ratio_13 = test3_quality['baseline_std'] / test1_quality['baseline_std']
    noise_ratio_23 = test3_quality['baseline_std'] / test2_quality['baseline_std']

    print(f"Test3 vs Test1 最大幅度比值: {max_amp_ratio_13:.1f}")
    print(f"Test3 vs Test2 最大幅度比值: {max_amp_ratio_23:.1f}")
    print(f"Test3 vs Test1 噪声水平比值: {noise_ratio_13:.1f}")
    print(f"Test3 vs Test2 噪声水平比值: {noise_ratio_23:.1f}")

    # 异常判断
    anomalies_detected = []

    if max_amp_ratio_13 > DIAGNOSTIC_THRESHOLDS['max_amplitude_ratio'] or max_amp_ratio_23 > DIAGNOSTIC_THRESHOLDS['max_amplitude_ratio']:
        anomalies_detected.append("幅度异常")
        print("⚠️ Test3幅度异常：相比Test1/Test2显著增大")
        analysis_results['recommendations'].append("Test3: 检查原始数据质量，可能存在伪迹或设备问题")

    if noise_ratio_13 > DIAGNOSTIC_THRESHOLDS['noise_level_ratio'] or noise_ratio_23 > DIAGNOSTIC_THRESHOLDS['noise_level_ratio']:
        anomalies_detected.append("噪声异常")
        print("⚠️ Test3噪声异常：相比Test1/Test2显著增大")
        analysis_results['recommendations'].append("Test3: 噪声水平过高，需要检查实验环境或设备状态")

    analysis_results['technical_diagnosis']['anomalies'] = anomalies_detected
    analysis_results['technical_diagnosis']['amplitude_ratios'] = {
        'test3_vs_test1': max_amp_ratio_13,
        'test3_vs_test2': max_amp_ratio_23
    }
    analysis_results['technical_diagnosis']['noise_ratios'] = {
        'test3_vs_test1': noise_ratio_13,
        'test3_vs_test2': noise_ratio_23
    }

    return analysis_results

def detect_double_peaks(test1_data, test2_data, test3_data, times, ch_names, sampling_freq):
    """双峰现象检测分析"""
    print("\n4. 双峰现象检测分析")
    print("-" * 40)

    # 选择前额叶区域进行分析
    frontal_channels = ['Fp1', 'Fp2', 'Fpz', 'AF3', 'AF4', 'AFz', 'F3', 'F4', 'Fz']
    frontal_indices = [i for i, ch in enumerate(ch_names) if ch in frontal_channels]

    if not frontal_indices:
        print("⚠️ 警告: 未找到前额叶电极，使用所有电极")
        frontal_indices = list(range(len(ch_names)))

    print(f"使用电极: {[ch_names[i] for i in frontal_indices]}")

    peak_analysis = {}

    for phase_name, data in [('Test1', test1_data), ('Test2', test2_data), ('Test3', test3_data)]:
        # 计算前额叶区域的平均波形
        frontal_avg = np.mean(data[:, frontal_indices, :], axis=(0, 1)) * 1e6

        # 在HEP窗口内检测峰值
        hep_mask = (times >= HEP_WINDOW[0]) & (times <= HEP_WINDOW[1])
        hep_signal = frontal_avg[hep_mask]
        hep_times = times[hep_mask] * 1000  # 转换为ms

        # 使用find_peaks检测峰值
        peaks, properties = find_peaks(np.abs(hep_signal),
                                     height=np.max(np.abs(hep_signal)) * DIAGNOSTIC_THRESHOLDS['peak_detection_threshold'],
                                     distance=int(0.05 * sampling_freq))  # 最小间距50ms

        peak_count = len(peaks)
        peak_times = hep_times[peaks] if peak_count > 0 else []
        peak_amplitudes = hep_signal[peaks] if peak_count > 0 else []

        peak_analysis[phase_name.lower()] = {
            'count': peak_count,
            'times': peak_times.tolist() if len(peak_times) > 0 else [],
            'amplitudes': peak_amplitudes.tolist() if len(peak_amplitudes) > 0 else [],
            'signal': hep_signal.tolist(),
            'time_axis': hep_times.tolist()
        }

        print(f"{phase_name}: 检测到 {peak_count} 个显著峰值")
        if peak_count >= 2:
            print(f"  ⚠️ 存在多峰现象！")
            for i, (time, amp) in enumerate(zip(peak_times, peak_amplitudes)):
                print(f"    峰值 {i+1}: 时间 {time:.1f}ms, 幅度 {amp:.2f}μV")
        elif peak_count == 1:
            print(f"  ✅ 单峰模式正常")
            print(f"    峰值: 时间 {peak_times[0]:.1f}ms, 幅度 {peak_amplitudes[0]:.2f}μV")
        else:
            print(f"  ⚠️ 未检测到明显峰值")

    # 特别关注Test3的双峰现象
    test3_peaks = peak_analysis['test3']
    double_peak_confirmed = test3_peaks['count'] >= 2

    if double_peak_confirmed:
        print(f"\n🔍 Test3双峰现象确认:")
        print(f"  检测到 {test3_peaks['count']} 个峰值")
        print(f"  这表明Test3阶段确实存在异常的多峰模式")

        # 分析峰值间距
        if len(test3_peaks['times']) >= 2:
            peak_intervals = np.diff(test3_peaks['times'])
            print(f"  峰值间距: {peak_intervals}")
            print(f"  平均间距: {np.mean(peak_intervals):.1f}ms")

    return peak_analysis, double_peak_confirmed

def create_comprehensive_diagnostic_visualization(test1_data, test2_data, test3_data, times, ch_names,
                                                analysis_results, peak_analysis):
    """创建综合诊断可视化图表"""
    print("\n5. 创建综合诊断可视化图表")
    print("-" * 40)

    # 创建大图表：3行2列布局
    fig = plt.figure(figsize=(16, 12))

    # 设置总标题
    fig.suptitle('Test3双峰现象综合诊断分析报告\n'
                 '双侧乳突参考 (TP9/TP10) | 0.5-45Hz + 高斯平滑',
                 fontsize=16, fontweight='bold', y=0.95)

    # 创建子图布局
    gs = fig.add_gridspec(3, 2, hspace=0.4, wspace=0.3)

    # 高对比度颜色方案
    phase_colors = {
        'Test1': '#FF4500',   # 深橙色
        'Test2': '#4B0082',   # 深紫色
        'Test3': '#8B4513'    # 深棕色
    }

    # 1. 前额叶区域HEP波形对比 (左上)
    ax1 = fig.add_subplot(gs[0, 0])

    # 计算前额叶区域平均波形
    frontal_channels = ['Fp1', 'Fp2', 'Fpz', 'AF3', 'AF4', 'AFz', 'F3', 'F4', 'Fz']
    frontal_indices = [i for i, ch in enumerate(ch_names) if ch in frontal_channels]

    if frontal_indices:
        test1_frontal = np.mean(test1_data[:, frontal_indices, :], axis=(0, 1)) * 1e6
        test2_frontal = np.mean(test2_data[:, frontal_indices, :], axis=(0, 1)) * 1e6
        test3_frontal = np.mean(test3_data[:, frontal_indices, :], axis=(0, 1)) * 1e6

        ax1.plot(times * 1000, test1_frontal, color=phase_colors['Test1'],
                linewidth=1.5, label='Test1', alpha=0.9)
        ax1.plot(times * 1000, test2_frontal, color=phase_colors['Test2'],
                linewidth=1.5, label='Test2', alpha=0.9)
        ax1.plot(times * 1000, test3_frontal, color=phase_colors['Test3'],
                linewidth=1.5, label='Test3', alpha=0.9)

    ax1.set_xlim(-200, 650)
    ax1.set_xlabel('时间 (ms)', fontsize=10)
    ax1.set_ylabel('幅值 (μV)', fontsize=10)
    ax1.set_title('前额叶区域HEP波形对比', fontsize=12, fontweight='bold')
    ax1.grid(True, alpha=0.3)
    ax1.axhline(0, color='black', linewidth=0.5, alpha=0.6)
    ax1.axvline(0, color='gray', linewidth=0.5, alpha=0.5, linestyle='--')
    ax1.axvline(200, color='red', linewidth=0.5, alpha=0.7, linestyle=':')
    ax1.axvline(600, color='red', linewidth=0.5, alpha=0.7, linestyle=':')
    ax1.legend(fontsize=9)

    # 2. 信号质量对比柱状图 (右上)
    ax2 = fig.add_subplot(gs[0, 1])

    phases = ['Test1', 'Test2', 'Test3']
    max_amplitudes = [analysis_results['data_quality'][p.lower()]['max_amplitude'] for p in phases]
    baseline_stds = [analysis_results['data_quality'][p.lower()]['baseline_std'] for p in phases]

    x = np.arange(len(phases))
    width = 0.35

    bars1 = ax2.bar(x - width/2, max_amplitudes, width, label='最大幅度 (μV)',
                   color=['#FF4500', '#4B0082', '#8B4513'], alpha=0.7)
    ax2_twin = ax2.twinx()
    bars2 = ax2_twin.bar(x + width/2, baseline_stds, width, label='基线标准差 (μV)',
                        color=['#FF6347', '#6A5ACD', '#CD853F'], alpha=0.7)

    ax2.set_xlabel('测试阶段', fontsize=10)
    ax2.set_ylabel('最大幅度 (μV)', fontsize=10)
    ax2_twin.set_ylabel('基线标准差 (μV)', fontsize=10)
    ax2.set_title('信号质量对比', fontsize=12, fontweight='bold')
    ax2.set_xticks(x)
    ax2.set_xticklabels(phases)
    ax2.legend(loc='upper left')
    ax2_twin.legend(loc='upper right')

    # 3. Test3峰值检测结果 (左中)
    ax3 = fig.add_subplot(gs[1, 0])

    if 'test3' in peak_analysis:
        test3_signal = np.array(peak_analysis['test3']['signal'])
        test3_times = np.array(peak_analysis['test3']['time_axis'])
        test3_peak_times = peak_analysis['test3']['times']
        test3_peak_amps = peak_analysis['test3']['amplitudes']

        ax3.plot(test3_times, test3_signal, color=phase_colors['Test3'],
                linewidth=2, label='Test3 HEP信号')

        # 标记检测到的峰值
        if test3_peak_times and test3_peak_amps:
            for i, (time, amp) in enumerate(zip(test3_peak_times, test3_peak_amps)):
                ax3.plot(time, amp, 'ro', markersize=8, label=f'峰值{i+1}' if i < 3 else '')
                ax3.annotate(f'{time:.0f}ms', (time, amp),
                           xytext=(5, 5), textcoords='offset points', fontsize=8)

    ax3.set_xlabel('时间 (ms)', fontsize=10)
    ax3.set_ylabel('幅值 (μV)', fontsize=10)
    ax3.set_title('Test3峰值检测结果', fontsize=12, fontweight='bold')
    ax3.grid(True, alpha=0.3)
    ax3.axhline(0, color='black', linewidth=0.5, alpha=0.6)
    ax3.legend(fontsize=9)

    # 4. 异常指标雷达图 (右中)
    ax4 = fig.add_subplot(gs[1, 1], projection='polar')

    # 计算异常指标
    test3_quality = analysis_results['data_quality']['test3']
    test1_quality = analysis_results['data_quality']['test1']
    test2_quality = analysis_results['data_quality']['test2']

    # 归一化指标 (相对于Test1和Test2的平均值)
    avg_max_amp = (test1_quality['max_amplitude'] + test2_quality['max_amplitude']) / 2
    avg_baseline_std = (test1_quality['baseline_std'] + test2_quality['baseline_std']) / 2
    avg_snr = (test1_quality['snr'] + test2_quality['snr']) / 2

    indicators = [
        test3_quality['max_amplitude'] / avg_max_amp,
        test3_quality['baseline_std'] / avg_baseline_std,
        1 / (test3_quality['snr'] / avg_snr) if avg_snr > 0 else 1,  # 信噪比越低越异常
        len(peak_analysis.get('test3', {}).get('times', [])) / 1.5  # 峰值数量，正常应该是1个
    ]

    labels = ['幅度比值', '噪声比值', '信噪比异常', '峰值数量']

    # 创建雷达图
    angles = np.linspace(0, 2 * np.pi, len(indicators), endpoint=False).tolist()
    indicators += indicators[:1]  # 闭合图形
    angles += angles[:1]

    ax4.plot(angles, indicators, 'o-', linewidth=2, color='red', alpha=0.7)
    ax4.fill(angles, indicators, alpha=0.25, color='red')
    ax4.set_xticks(angles[:-1])
    ax4.set_xticklabels(labels, fontsize=9)
    ax4.set_ylim(0, max(3, max(indicators)))
    ax4.set_title('Test3异常指标', fontsize=12, fontweight='bold', pad=20)
    ax4.grid(True)

    # 5. 诊断结论文本 (下方跨两列)
    ax5 = fig.add_subplot(gs[2, :])
    ax5.axis('off')

    # 生成诊断结论文本
    conclusion_text = generate_diagnostic_conclusion(analysis_results, peak_analysis)
    ax5.text(0.05, 0.95, conclusion_text, transform=ax5.transAxes, fontsize=11,
             verticalalignment='top', horizontalalignment='left',
             bbox=dict(boxstyle='round,pad=0.5', facecolor='lightgray', alpha=0.8))

    plt.tight_layout()

    # 保存图片
    output_file = os.path.join(RESULT_DIR, 'test3_comprehensive_diagnostic_analysis.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"综合诊断图表已保存: {output_file}")

    plt.show()
    return fig

def generate_diagnostic_conclusion(analysis_results, peak_analysis):
    """生成诊断结论文本"""
    conclusion = "Test3双峰现象诊断结论:\n\n"

    # 1. 双峰现象确认
    test3_peaks = peak_analysis.get('test3', {})
    peak_count = test3_peaks.get('count', 0)

    if peak_count >= 2:
        conclusion += f"✓ 确认存在双峰现象: 检测到{peak_count}个显著峰值\n"
    else:
        conclusion += f"✗ 未检测到明显双峰现象: 仅检测到{peak_count}个峰值\n"

    # 2. 数据质量问题
    anomalies = analysis_results['technical_diagnosis'].get('anomalies', [])
    if anomalies:
        conclusion += f"✓ 数据质量异常: {', '.join(anomalies)}\n"
    else:
        conclusion += "✗ 数据质量正常\n"

    # 3. 主要建议
    recommendations = analysis_results.get('recommendations', [])
    if recommendations:
        conclusion += f"\n主要建议:\n"
        for i, rec in enumerate(recommendations[:3], 1):  # 最多显示3个建议
            conclusion += f"{i}. {rec}\n"

    return conclusion

def save_comprehensive_diagnostic_report(analysis_results, peak_analysis, times):
    """保存综合诊断报告"""
    print("\n6. 保存综合诊断报告")
    print("-" * 40)

    report_file = os.path.join(RESULT_DIR, 'test3_comprehensive_diagnostic_report.txt')

    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("Test3双峰现象综合诊断报告\n")
        f.write("=" * 80 + "\n\n")
        f.write(f"分析时间: {times[0]*1000:.0f} 到 {times[-1]*1000:.0f} ms\n")
        f.write(f"基线矫正窗口: {BASELINE_WINDOW[0]*1000:.0f} 到 {BASELINE_WINDOW[1]*1000:.0f} ms\n")
        f.write(f"HEP分析窗口: {HEP_WINDOW[0]*1000:.0f} 到 {HEP_WINDOW[1]*1000:.0f} ms\n")
        f.write(f"滤波设置: 0.5-45Hz + 高斯平滑\n")
        f.write(f"参考方式: TP9/TP10双侧乳突参考\n\n")

        # 1. 执行摘要
        f.write("1. 执行摘要\n")
        f.write("-" * 40 + "\n")

        # 双峰现象确认
        test3_peaks = peak_analysis.get('test3', {})
        peak_count = test3_peaks.get('count', 0)

        if peak_count >= 2:
            f.write(f"⚠️ 确认存在双峰现象: Test3阶段检测到{peak_count}个显著峰值\n")
            f.write(f"这表明Test3阶段的HEP信号存在异常的多峰模式\n\n")
        else:
            f.write(f"✅ 未检测到明显双峰现象: Test3阶段仅检测到{peak_count}个峰值\n\n")

        # 数据质量问题
        anomalies = analysis_results['technical_diagnosis'].get('anomalies', [])
        if anomalies:
            f.write(f"⚠️ 数据质量异常: {', '.join(anomalies)}\n")
        else:
            f.write(f"✅ 数据质量正常\n")
        f.write("\n")

        # 2. 详细分析结果
        f.write("2. 详细分析结果\n")
        f.write("-" * 40 + "\n\n")

        # 2.1 数据量统计
        f.write("2.1 数据量统计\n")
        epoch_counts = analysis_results['summary']['epoch_counts']
        for phase, count in epoch_counts.items():
            f.write(f"{phase.upper()}: {count} epochs\n")
        f.write("\n")

        # 2.2 信号质量分析
        f.write("2.2 信号质量分析\n")
        for phase in ['test1', 'test2', 'test3']:
            quality = analysis_results['data_quality'][phase]
            f.write(f"{phase.upper()}:\n")
            f.write(f"  最大幅度: {quality['max_amplitude']:.2f} μV\n")
            f.write(f"  平均幅度: {quality['mean_amplitude']:.2f} μV\n")
            f.write(f"  信号标准差: {quality['std_amplitude']:.2f} μV\n")
            f.write(f"  基线标准差: {quality['baseline_std']:.2f} μV\n")
            f.write(f"  HEP窗口标准差: {quality['hep_std']:.2f} μV\n")
            f.write(f"  估算信噪比: {quality['snr']:.2f}\n\n")

        # 2.3 Test3异常检测
        f.write("2.3 Test3异常检测\n")
        amplitude_ratios = analysis_results['technical_diagnosis']['amplitude_ratios']
        noise_ratios = analysis_results['technical_diagnosis']['noise_ratios']

        f.write(f"幅度比值分析:\n")
        f.write(f"  Test3 vs Test1: {amplitude_ratios['test3_vs_test1']:.1f}\n")
        f.write(f"  Test3 vs Test2: {amplitude_ratios['test3_vs_test2']:.1f}\n")
        f.write(f"噪声比值分析:\n")
        f.write(f"  Test3 vs Test1: {noise_ratios['test3_vs_test1']:.1f}\n")
        f.write(f"  Test3 vs Test2: {noise_ratios['test3_vs_test2']:.1f}\n\n")

        # 2.4 峰值检测结果
        f.write("2.4 峰值检测结果\n")
        for phase in ['test1', 'test2', 'test3']:
            if phase in peak_analysis:
                peaks = peak_analysis[phase]
                f.write(f"{phase.upper()}: 检测到{peaks['count']}个峰值\n")
                if peaks['times'] and peaks['amplitudes']:
                    for i, (time, amp) in enumerate(zip(peaks['times'], peaks['amplitudes'])):
                        f.write(f"  峰值{i+1}: 时间{time:.1f}ms, 幅度{amp:.2f}μV\n")
                f.write("\n")

        # 3. 根本原因分析
        f.write("3. 根本原因分析\n")
        f.write("-" * 40 + "\n")

        if peak_count >= 2:
            f.write("Test3双峰现象的可能原因:\n\n")
            f.write("3.1 数据质量问题:\n")
            if amplitude_ratios['test3_vs_test1'] > DIAGNOSTIC_THRESHOLDS['max_amplitude_ratio']:
                f.write("  - 信号幅度异常增大，可能存在伪迹污染\n")
            if noise_ratios['test3_vs_test1'] > DIAGNOSTIC_THRESHOLDS['noise_level_ratio']:
                f.write("  - 噪声水平显著增高，可能是环境干扰或设备问题\n")

            f.write("\n3.2 技术处理问题:\n")
            f.write("  - R波检测精度下降，导致epoch对齐不准确\n")
            f.write("  - 心跳间期变异性增大，影响平均波形质量\n")
            f.write("  - 滤波参数不适合Test3阶段的信号特征\n")

            f.write("\n3.3 生理状态变化:\n")
            f.write("  - Test3阶段被试的生理状态可能发生变化\n")
            f.write("  - 心率变异性增加，影响HEP信号的一致性\n")
            f.write("  - 注意力或觉醒水平的变化\n")

        # 4. 解决方案建议
        f.write("\n4. 解决方案建议\n")
        f.write("-" * 40 + "\n")

        recommendations = analysis_results.get('recommendations', [])
        if recommendations:
            f.write("基于分析结果的具体建议:\n\n")
            for i, rec in enumerate(recommendations, 1):
                f.write(f"{i}. {rec}\n")

        f.write("\n通用优化建议:\n")
        f.write("1. 检查原始ECG信号质量，重新优化R波检测算法\n")
        f.write("2. 使用更严格的心跳间期筛选标准(0.6-1.2秒)\n")
        f.write("3. 对Test3阶段使用专门的预处理参数\n")
        f.write("4. 考虑使用自适应滤波或更保守的滤波参数\n")
        f.write("5. 增加数据量或改进epoch提取策略\n")
        f.write("6. 检查实验环境和设备状态\n")
        f.write("7. 考虑分段分析，识别具体的问题时间段\n")

        # 5. 技术参数建议
        f.write("\n5. 技术参数建议\n")
        f.write("-" * 40 + "\n")
        f.write("针对Test3阶段的优化参数:\n")
        f.write("- 滤波范围: 1.0-30Hz (更保守)\n")
        f.write("- 基线矫正: -300ms到-50ms (避开刺激影响)\n")
        f.write("- 伪迹阈值: 降低到75μV\n")
        f.write("- R-R间期: 0.6-1.2秒 (更严格)\n")
        f.write("- 平滑参数: 高斯σ=2.0 (更强平滑)\n")

    print(f"综合诊断报告已保存: {report_file}")

def main():
    """主函数 - Test3双峰现象综合诊断分析"""
    print("=" * 80)
    print("Test3双峰现象综合诊断分析")
    print("双侧乳突参考 (TP9/TP10)")
    print("=" * 80)

    print(f"时间窗口: -200 到 650 ms")
    print(f"基线矫正: {BASELINE_WINDOW[0]*1000:.0f} 到 {BASELINE_WINDOW[1]*1000:.0f} ms")
    print(f"HEP分析: {HEP_WINDOW[0]*1000:.0f} 到 {HEP_WINDOW[1]*1000:.0f} ms")
    print(f"滤波设置: 0.5-45Hz + 高斯平滑")
    print("分析目标: 全面诊断Test3双峰现象并提供解决方案")

    try:
        # 步骤1: 查找并加载数据文件
        print("\n步骤1: 加载三个测试阶段数据文件")
        test1_file, test2_file, test3_file = find_latest_files()

        # 加载三个阶段的数据
        test1_data, ch_names, times, _, sampling_freq = load_hep_data(test1_file)
        test2_data, _, _, _, _ = load_hep_data(test2_file)
        test3_data, _, _, _, _ = load_hep_data(test3_file)

        # 步骤2: 数据预处理
        print("\n步骤2: 数据预处理")
        test1_data = apply_bandpass_filter(test1_data, sampling_freq, 0.5, 45)
        test2_data = apply_bandpass_filter(test2_data, sampling_freq, 0.5, 45)
        test3_data = apply_bandpass_filter(test3_data, sampling_freq, 0.5, 45)

        # 步骤3: 统一基线矫正
        print("\n步骤3: 统一基线矫正")
        test1_data, test2_data, test3_data = apply_unified_baseline_correction_three_phases(
            test1_data, test2_data, test3_data, times)

        # 步骤4: 信号平滑
        print("\n步骤4: 信号平滑")
        test1_data = apply_gaussian_smoothing(test1_data, 1.5)
        test2_data = apply_gaussian_smoothing(test2_data, 1.5)
        test3_data = apply_gaussian_smoothing(test3_data, 1.5)

        # 步骤5: 综合分析
        analysis_results = comprehensive_test3_analysis(
            test1_data, test2_data, test3_data, times, ch_names, sampling_freq)

        # 步骤6: 双峰检测
        peak_analysis, double_peak_confirmed = detect_double_peaks(
            test1_data, test2_data, test3_data, times, ch_names, sampling_freq)

        # 更新分析结果
        analysis_results['double_peak_analysis'] = peak_analysis
        analysis_results['double_peak_confirmed'] = double_peak_confirmed

        # 步骤7: 创建综合可视化
        fig = create_comprehensive_diagnostic_visualization(
            test1_data, test2_data, test3_data, times, ch_names,
            analysis_results, peak_analysis)

        # 步骤8: 保存综合报告
        save_comprehensive_diagnostic_report(analysis_results, peak_analysis, times)

        # 输出分析总结
        print("\n" + "=" * 80)
        print("Test3双峰现象综合诊断分析完成！")
        print("=" * 80)

        # 输出关键发现
        if double_peak_confirmed:
            print("🔍 关键发现: Test3阶段确实存在双峰现象")
            test3_peaks = peak_analysis.get('test3', {})
            print(f"   检测到 {test3_peaks.get('count', 0)} 个显著峰值")

            # 输出异常指标
            anomalies = analysis_results['technical_diagnosis'].get('anomalies', [])
            if anomalies:
                print(f"   数据质量异常: {', '.join(anomalies)}")

            print("   建议: 需要优化数据预处理流程和R波检测算法")
        else:
            print("✅ 关键发现: 当前分析未检测到明显的双峰现象")
            print("   建议: 继续监控数据质量，保持当前预处理流程")

        # 输出数据统计
        print(f"\n📊 数据统计:")
        epoch_counts = analysis_results['summary']['epoch_counts']
        for phase, count in epoch_counts.items():
            print(f"   {phase.upper()}: {count} epochs")

        # 输出质量指标
        print(f"\n📈 质量指标:")
        for phase in ['test1', 'test2', 'test3']:
            quality = analysis_results['data_quality'][phase]
            print(f"   {phase.upper()}: 最大幅度{quality['max_amplitude']:.0f}μV, "
                  f"基线标准差{quality['baseline_std']:.1f}μV, "
                  f"信噪比{quality['snr']:.1f}")

        print(f"\n📁 结果保存目录: {RESULT_DIR}")
        print("   - test3_comprehensive_diagnostic_analysis.png (综合诊断图表)")
        print("   - test3_comprehensive_diagnostic_report.txt (详细诊断报告)")

    except Exception as e:
        print(f"❌ 分析过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
